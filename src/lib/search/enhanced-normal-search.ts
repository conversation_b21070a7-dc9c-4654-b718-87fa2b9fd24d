import * as Commerce from "commerce-kit";
import { safeProductBrowse } from "@/lib/safe-commerce";
import Fuse, { type IFuseOptions } from "fuse.js";
import { unstable_cache } from "next/cache";

export interface NormalSearchFilters {
	category?: string;
	priceRange?: {
		min: number;
		max: number;
	};
	sortBy?: "relevance" | "price-asc" | "price-desc" | "name" | "newest";
}

export interface NormalSearchResult {
	products: Commerce.MappedProduct[];
	totalCount: number;
	categories: string[];
	priceRange: {
		min: number;
		max: number;
	};
}

export interface NormalSearchOptions {
	query: string;
	filters?: NormalSearchFilters;
	limit?: number;
	offset?: number;
}